package main

import (
	"fmt"
	"time"

	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/logging"
	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/logging/logfields"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

var (
	// 版本信息
	version = "1.0.0"

	// 命令行选项
	showTimestamp bool

	// 根命令
	rootCmd = &cobra.Command{
		Use:   "logger-demo",
		Short: "A demo application using CCE network logger",
		Long:  `A demo application that demonstrates the same logging system used in CCE network plugins.`,
		Run:   runLoggerDemo,
	}

	// 全局logger，类似于CNI插件中的使用方式
	logger *logrus.Entry
)

func init() {
	// 添加命令行选项
	rootCmd.Flags().BoolVar(&showTimestamp, "timestamp", false, "Show timestamp in log output (default: false, same as CCE network)")

	// 添加版本命令
	rootCmd.AddCommand(&cobra.Command{
		Use:   "version",
		Short: "Print version information",
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Printf("logger-demo version %s\n", version)
		},
	})
}

func setupLogging() error {
	// 使用与CNI插件相同的日志设置
	// 这会设置syslog输出，与roce插件中的setupLogging函数类似
	if err := logging.SetupCNILogging("logger-demo", false); err != nil {
		return fmt.Errorf("failed to setup logging: %v", err)
	}

	// 创建带有字段的logger，类似于CNI插件中的做法
	logger = logging.DefaultLogger.WithFields(logrus.Fields{
		"reqID":     uuid.New().String(),
		"plugin":    "logger-demo",
		"component": "demo",
		"version":   version,
	})

	return nil
}

func setupLoggingWithTimestamp() error {
	// 创建一个带时间戳的logger版本（用于演示）
	customLogger := logrus.New()
	customLogger.SetFormatter(&logrus.TextFormatter{
		DisableTimestamp: false,  // 启用时间戳
		FullTimestamp:    true,
		TimestampFormat:  "2006-01-02 15:04:05",
		DisableColors:    true,
	})
	customLogger.SetLevel(logrus.InfoLevel)

	// 创建带有字段的logger
	logger = customLogger.WithFields(logrus.Fields{
		"reqID":     uuid.New().String(),
		"plugin":    "logger-demo",
		"component": "demo",
		"version":   version,
	})

	return nil
}

func runLoggerDemo(cmd *cobra.Command, args []string) {
	// 根据命令行选项初始化日志系统
	var err error
	if showTimestamp {
		err = setupLoggingWithTimestamp()
		fmt.Println("Using custom logger with timestamp (for demonstration)")
	} else {
		err = setupLogging()
		fmt.Println("Using CCE network logger format (no timestamp, same as original)")
	}

	if err != nil {
		fmt.Printf("Failed to setup logging: %v\n", err)
		return
	}

	logger.Info("====> Logger Demo Begins <====")
	defer logger.Info("====> Logger Demo Ends <====")

	// 模拟类似allocateRDMAIPsWithCCEAgent函数中的日志输出
	simulateRDMAAllocation()

	// 演示不同级别的日志输出
	demonstrateLogLevels()

	// 演示结构化日志字段
	demonstrateStructuredLogging()

	// 演示时间相关的日志
	demonstrateTimeLogging()

	logger.Info("All logging demonstrations completed successfully")
}

func simulateRDMAAllocation() {
	// 模拟allocateRDMAIPsWithCCEAgent函数中的日志输出
	podName := "demo-namespace/demo-pod"
	netns := "/var/run/netns/demo-netns"
	containerID := "demo-container-12345"

	// 这行日志与原函数中的格式完全一致
	logger.Infof("Allocated RDMAIPAM for pod %s in ns %s for container %s", podName, netns, containerID)

	// 添加一些额外的模拟日志
	logger.WithFields(logrus.Fields{
		logfields.K8sPodName:   "demo-pod",
		logfields.K8sNamespace: "demo-namespace",
		logfields.ContainerID:  containerID,
		logfields.NetNSName:    netns,
	}).Info("RDMA allocation simulation completed")
}

func demonstrateLogLevels() {
	logger.Debug("This is a debug message")
	logger.Info("This is an info message")
	logger.Warn("This is a warning message")
	logger.Error("This is an error message")
}

func demonstrateStructuredLogging() {
	// 使用logfields包中的常量进行结构化日志记录
	logger.WithFields(logrus.Fields{
		logfields.IPAddr:      "*************",
		logfields.MACAddr:     "aa:bb:cc:dd:ee:ff",
		logfields.Interface:   "eth0",
		logfields.EndpointID:  "12345",
		logfields.ServiceName: "demo-service",
		logfields.BackendID:   "backend-001",
	}).Info("Network interface configuration")

	// 演示使用Json辅助函数
	demoData := map[string]interface{}{
		"timestamp": time.Now().Unix(),
		"status":    "active",
		"metrics": map[string]int{
			"connections": 42,
			"bandwidth":   1000,
		},
	}

	logger.WithFields(logrus.Fields{
		"data":             logfields.Json(demoData),
		logfields.Duration: "150ms",
		logfields.Count:    42,
	}).Info("Service metrics collected")
}

func demonstrateTimeLogging() {
	startTime := time.Now()

	// 模拟一些工作
	time.Sleep(100 * time.Millisecond)

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	logger.WithFields(logrus.Fields{
		logfields.StartTime: startTime.Format(time.RFC3339),
		logfields.EndTime:   endTime.Format(time.RFC3339),
		logfields.Duration:  duration.String(),
		"operation":         "demo-task",
	}).Info("Task execution completed")

	// 使用Unix时间戳
	logger.WithFields(logrus.Fields{
		"timestamp":    time.Now().Unix(),
		"timezone":     time.Now().Location().String(),
		"weekday":      time.Now().Weekday().String(),
		logfields.Node: "demo-node-001",
	}).Info("Current system time information")
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Printf("Failed to execute command: %v\n", err)
	}
}
